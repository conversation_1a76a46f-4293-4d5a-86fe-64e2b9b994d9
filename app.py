import streamlit as st
import torch
from ultralytics import YOLO
from PIL import Image
import numpy as np
import io
import traceback

# Configure page
st.set_page_config(
    page_title="Road Classification App",
    page_icon="🛣️",
    layout="wide"
)

# Initialize session state
if 'model' not in st.session_state:
    st.session_state.model = None
if 'model_loaded' not in st.session_state:
    st.session_state.model_loaded = False

@st.cache_resource
def load_model(model_path, device='cpu'):
    """Load YOLOv11 model with caching"""
    try:
        model = YOLO(model_path)
        model.to(device)
        return model, True, "Model loaded successfully!"
    except Exception as e:
        return None, False, f"Error loading model: {str(e)}"

def predict_image(model, image, conf_threshold=0.8, img_size=640):
    """
    Perform inference on image with robust error handling
    Returns: (predicted_class, confidence, is_accepted, raw_results)
    """
    try:
        # Convert PIL image to format suitable for YOLO
        if isinstance(image, Image.Image):
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
        
        # Perform inference
        results = model(
            image, 
            imgsz=img_size, 
            conf=0.01,  # Set very low conf for inference, we'll filter later
            verbose=False
        )
        
        # Check if results exist and have detections
        if not results or len(results) == 0:
            return "Unknown", 0.00, False, None
        
        result = results[0]
        
        # Check if there are any detections
        if result.probs is None:
            return "Unknown", 0.00, False, result
        
        # Get the top prediction
        probs = result.probs
        if probs.data is None or len(probs.data) == 0:
            return "Unknown", 0.00, False, result
        
        # Get top class and confidence
        top_class_idx = probs.top1
        confidence = float(probs.top1conf)
        
        # Map class index to class name
        class_names = model.names if hasattr(model, 'names') else {0: 'no road', 1: 'road'}
        predicted_class_raw = class_names.get(top_class_idx, 'unknown')
        
        # Format class name for display
        if predicted_class_raw.lower() == 'road':
            predicted_class = "Road"
        elif predicted_class_raw.lower() == 'no road':
            predicted_class = "No Road"
        else:
            predicted_class = "Unknown"
        
        # Apply confidence threshold
        if confidence >= conf_threshold:
            return predicted_class, confidence, True, result
        else:
            return "Unknown", confidence, False, result
            
    except Exception as e:
        st.error(f"Prediction error: {str(e)}")
        return "Unknown", 0.00, False, None

def main():
    st.title("🛣️ Road Classification App")
    st.markdown("### YOLOv11m Two-Class Classifier: Road vs No Road")
    
    # Sidebar configuration
    st.sidebar.header("⚙️ Configuration")
    
    # Model loading section
    st.sidebar.subheader("📁 Model Loading")
    model_path = st.sidebar.text_input(
        "Model Path (.pt file)", 
        value="best.pt",
        help="Path to your trained YOLOv11m model file"
    )
    
    # Device selection
    device_options = ['cpu']
    if torch.cuda.is_available():
        device_options.append('cuda')
    
    selected_device = st.sidebar.selectbox(
        "🔧 Device", 
        device_options,
        index=0
    )
    
    # Load model button
    if st.sidebar.button("🚀 Load Model"):
        with st.spinner("Loading model..."):
            model, success, message = load_model(model_path, selected_device)
            if success:
                st.session_state.model = model
                st.session_state.model_loaded = True
                st.sidebar.success(message)
            else:
                st.sidebar.error(message)
                st.session_state.model_loaded = False
    
    # Show model status
    if st.session_state.model_loaded:
        st.sidebar.success("✅ Model Ready")
    else:
        st.sidebar.warning("⚠️ Model Not Loaded")
    
    st.sidebar.markdown("---")
    
    # Inference parameters
    st.sidebar.subheader("🎯 Inference Parameters")
    
    conf_threshold = st.sidebar.slider(
        "Confidence Threshold",
        min_value=0.1,
        max_value=1.0,
        value=0.8,
        step=0.05,
        help="Minimum confidence score for acceptance"
    )
    
    img_size = st.sidebar.selectbox(
        "Image Size",
        [416, 640, 800],
        index=1,
        help="Input image size for inference"
    )
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("📤 Upload Image")
        uploaded_file = st.file_uploader(
            "Choose an image...",
            type=['png', 'jpg', 'jpeg', 'bmp', 'tiff'],
            help="Upload an image to classify as Road or No Road"
        )
        
        if uploaded_file is not None:
            try:
                # Display uploaded image
                image = Image.open(uploaded_file)
                st.image(image, caption="Uploaded Image", use_column_width=True)
                
                # Store image info
                st.info(f"📊 Image Info: {image.size[0]}×{image.size[1]} pixels, Mode: {image.mode}")
                
            except Exception as e:
                st.error(f"Error loading image: {str(e)}")
                image = None
        else:
            image = None
    
    with col2:
        st.subheader("🎯 Classification Results")
        
        if image is not None and st.session_state.model_loaded:
            if st.button("🔍 Classify Image", type="primary"):
                with st.spinner("Classifying..."):
                    try:
                        # Perform prediction
                        predicted_class, confidence, is_accepted, raw_results = predict_image(
                            st.session_state.model, 
                            image, 
                            conf_threshold, 
                            img_size
                        )
                        
                        # Display results
                        st.markdown("### 📋 Results")
                        
                        # Result status
                        if is_accepted:
                            st.success("✅ **Status:** ACCEPTED")
                        else:
                            st.error("❌ **Status:** REJECTED")
                        
                        # Create results display
                        result_col1, result_col2 = st.columns(2)
                        
                        with result_col1:
                            st.metric(
                                label="🏷️ Predicted Class",
                                value=predicted_class
                            )
                        
                        with result_col2:
                            st.metric(
                                label="🎯 Confidence Score",
                                value=f"{confidence:.2f}"
                            )
                        
                        # Threshold info
                        st.info(f"🎚️ **Threshold:** {conf_threshold:.2f} | **Image Size:** {img_size}px")
                        
                        # Additional info based on result
                        if predicted_class == "Unknown":
                            st.warning(
                                "⚠️ **Classification rejected:** Low confidence or no clear detection. "
                                "This might be an unrelated image or unclear road/no-road content."
                            )
                        elif is_accepted:
                            if predicted_class == "Road":
                                st.success("🛣️ **High confidence road detection!**")
                            else:
                                st.success("🚫 **High confidence non-road detection!**")
                        else:
                            st.warning(f"⚠️ **Low confidence:** Model detected '{predicted_class}' but confidence ({confidence:.2f}) is below threshold ({conf_threshold:.2f})")
                        
                        # Debug information (expandable)
                        with st.expander("🔧 Debug Information"):
                            st.write("**Model Device:**", selected_device)
                            st.write("**Raw Confidence:**", confidence)
                            st.write("**Threshold Applied:**", conf_threshold)
                            st.write("**Image Size Used:**", img_size)
                            if hasattr(st.session_state.model, 'names'):
                                st.write("**Model Classes:**", st.session_state.model.names)
                        
                    except Exception as e:
                        st.error("❌ **Classification Failed**")
                        st.error(f"Error: {str(e)}")
                        
                        # Show detailed error in expandable section
                        with st.expander("🐛 Error Details"):
                            st.code(traceback.format_exc())
        
        elif not st.session_state.model_loaded:
            st.warning("⚠️ Please load a model first using the sidebar.")
        
        elif image is None:
            st.info("📤 Please upload an image to classify.")
    
    # Footer information
    st.markdown("---")
    st.markdown(
        """
        ### 📝 Instructions:
        1. **Load Model:** Enter your model path (e.g., `best.pt`) and click 'Load Model'
        2. **Upload Image:** Choose an image file to classify
        3. **Adjust Settings:** Use sidebar to modify confidence threshold and image size
        4. **Classify:** Click 'Classify Image' to get results
        
        ### 🎯 Classification Logic:
        - **Confidence ≥ threshold:** Shows predicted class (Road/No Road) - **ACCEPTED**
        - **Confidence < threshold:** Shows "Unknown" - **REJECTED**
        - **No detection/Error:** Shows "Unknown" with 0.00 confidence - **REJECTED**
        """
    )

if __name__ == "__main__":
    main()