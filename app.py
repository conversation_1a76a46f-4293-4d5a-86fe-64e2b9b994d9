import streamlit as st
import cv2
import numpy as np
from PIL import Image
import torch
from ultralytics import YOLO
import io
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Page configuration
st.set_page_config(
    page_title="Road Classifier - One-Class YOLO",
    page_icon="🛣️",
    layout="wide"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin: 1rem 0;
    }
    .accepted {
        border-left-color: #28a745 !important;
        background: #d4edda !important;
    }
    .rejected {
        border-left-color: #dc3545 !important;
        background: #f8d7da !important;
    }
    .threshold-info {
        background: #e3f2fd;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #2196f3;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Header
st.markdown("""
<div class="main-header">
    <h1>🛣️ Road Classifier - One-Class YOLO Detection</h1>
    <p>Upload an image to classify whether it contains a road using confidence-based rejection</p>
</div>
""", unsafe_allow_html=True)

# Sidebar configuration
st.sidebar.header("⚙️ Configuration")

# Model loading section
@st.cache_resource
def load_model(model_path):
    """Load the YOLOv11m model"""
    try:
        model = YOLO(model_path)
        return model
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        return None

# Model path input
model_path = st.sidebar.text_input(
    "Model Path (.pt file)", 
    value="best.pt",
    help="Enter the path to your trained YOLOv11m .pt model file"
)

# Confidence threshold slider
confidence_threshold = st.sidebar.slider(
    "Confidence Threshold", 
    min_value=0.1, 
    max_value=1.0, 
    value=0.8, 
    step=0.05,
    help="Images with confidence below this threshold will be rejected as 'Unknown'"
)

# Advanced settings
st.sidebar.subheader("Advanced Settings")
img_size = st.sidebar.selectbox(
    "Image Size for Inference",
    [320, 416, 512, 640, 832],
    index=3,
    help="Input image size for the model (larger = more accurate but slower)"
)

device = st.sidebar.selectbox(
    "Device",
    ["cpu", "cuda"] if torch.cuda.is_available() else ["cpu"],
    help="Processing device (CUDA if available)"
)

# Threshold explanation
st.markdown("""
<div class="threshold-info">
    <h3>🎯 Classification Logic</h3>
    <p><strong>One-Class Classifier Behavior:</strong></p>
    <ul>
        <li>✅ <strong>Road</strong>: Confidence > {:.1f} → Image contains a recognizable road</li>
        <li>❌ <strong>Unknown/Anomaly</strong>: Confidence ≤ {:.1f} → Image doesn't clearly contain a road or model is uncertain</li>
    </ul>
    <p><em>Note: This model was trained only on road images, so it acts as a one-class detector.</em></p>
</div>
""".format(confidence_threshold, confidence_threshold), unsafe_allow_html=True)

# Main content area
col1, col2 = st.columns([1, 1])

with col1:
    st.subheader("📤 Upload Image")
    uploaded_file = st.file_uploader(
        "Choose an image file",
        type=['png', 'jpg', 'jpeg', 'bmp', 'tiff'],
        help="Upload an image to classify (PNG, JPG, JPEG, BMP, TIFF supported)"
    )
    
    # Sample images for testing
    st.subheader("🧪 Test Cases")
    st.markdown("""
    **Expected Behaviors:**
    - **Road images**: High confidence → "Road"
    - **Non-road images**: Low confidence → "Unknown"
    - **Ambiguous images**: Low confidence → "Unknown"
    """)

# Load model
if model_path:
    model = load_model(model_path)
    if model is not None:
        st.sidebar.success("✅ Model loaded successfully!")
        model_info = f"Model: YOLOv11m | Classes: {len(model.names)} | Device: {device}"
        st.sidebar.info(model_info)
    else:
        st.sidebar.error("❌ Failed to load model")
        st.stop()
else:
    st.sidebar.warning("⚠️ Please enter model path")
    st.stop()

def preprocess_image(image):
    """Preprocess the uploaded image"""
    # Convert PIL image to numpy array
    img_array = np.array(image)
    
    # Convert RGB to BGR for OpenCV compatibility
    if len(img_array.shape) == 3 and img_array.shape[2] == 3:
        img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
    
    return img_array

def run_inference(model, image, img_size, device, conf_threshold):
    """Run inference on the image"""
    try:
        # Run prediction
        results = model.predict(
            source=image,
            imgsz=img_size,
            device=device,
            conf=0.01,  # Use very low conf to get all predictions
            verbose=False
        )
        
        # Extract results
        if len(results) > 0 and len(results[0].boxes) > 0:
            # Get the highest confidence detection
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy()
            
            # Find the detection with highest confidence
            max_conf_idx = np.argmax(confidences)
            max_confidence = float(confidences[max_conf_idx])
            predicted_class = int(classes[max_conf_idx])
            class_name = model.names[predicted_class]
            
            return max_confidence, class_name, results[0]
        else:
            # No detections found
            return 0.0, "road", results[0] if len(results) > 0 else None
            
    except Exception as e:
        st.error(f"Error during inference: {str(e)}")
        return 0.0, "road", None

def create_confidence_gauge(confidence, threshold):
    """Create a confidence gauge visualization"""
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        value = confidence,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': "Confidence Score"},
        delta = {'reference': threshold},
        gauge = {
            'axis': {'range': [None, 1]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, threshold], 'color': "lightgray"},
                {'range': [threshold, 1], 'color': "lightgreen"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': threshold
            }
        }
    ))
    
    fig.update_layout(height=300, font={'size': 16})
    return fig

# Main inference section
if uploaded_file is not None:
    with col2:
        st.subheader("🔍 Classification Results")
        
        # Load and display image
        image = Image.open(uploaded_file)
        st.image(image, caption="Uploaded Image", use_column_width=True)
        
        # Preprocess image
        img_array = preprocess_image(image)
        
        # Run inference
        with st.spinner('🔄 Running inference...'):
            confidence, predicted_class, results = run_inference(
                model, img_array, img_size, device, confidence_threshold
            )
        
        # Apply threshold logic
        if confidence > confidence_threshold:
            final_prediction = "Road"
            status = "accepted"
            status_icon = "✅"
            status_text = "ACCEPTED"
        else:
            final_prediction = "Unknown"
            status = "rejected"
            status_icon = "❌"
            status_text = "REJECTED"
        
        # Display results
        st.markdown(f"""
        <div class="metric-card {status}">
            <h3>{status_icon} {status_text}</h3>
            <h2>Prediction: {final_prediction}</h2>
            <p><strong>Confidence:</strong> {confidence:.3f}</p>
            <p><strong>Raw Model Output:</strong> {predicted_class}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Confidence gauge
        fig = create_confidence_gauge(confidence, confidence_threshold)
        st.plotly_chart(fig, use_container_width=True)
        
        # Detailed analysis
        st.subheader("📊 Detailed Analysis")
        
        col_a, col_b, col_c = st.columns(3)
        with col_a:
            st.metric("Confidence Score", f"{confidence:.3f}")
        with col_b:
            st.metric("Threshold", f"{confidence_threshold:.3f}")
        with col_c:
            margin = confidence - confidence_threshold
            st.metric("Margin", f"{margin:+.3f}")
        
        # Decision explanation
        st.markdown("**Decision Logic:**")
        if confidence > confidence_threshold:
            st.success(f"✅ Confidence ({confidence:.3f}) > Threshold ({confidence_threshold:.3f}) → Classified as **Road**")
        else:
            st.error(f"❌ Confidence ({confidence:.3f}) ≤ Threshold ({confidence_threshold:.3f}) → Classified as **Unknown**")
        
        # Additional information
        st.subheader("ℹ️ Model Information")
        st.info(f"""
        **Model Type:** YOLOv11m One-Class Classifier  
        **Training Data:** Road images only  
        **Input Size:** {img_size}px  
        **Device:** {device}  
        **Confidence Threshold:** {confidence_threshold}
        """)

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666;">
    <p>🛣️ YOLOv11m Road Classifier | One-Class Detection with Confidence-Based Rejection</p>
</div>
""", unsafe_allow_html=True)